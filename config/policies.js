/**
 * Policy Mappings
 * (sails.config.policies)
 *
 * Policies are simple functions which run **before** your actions.
 *
 * For more information on configuring policies, check out:
 * https://sailsjs.com/docs/concepts/policies
 */

module.exports.policies = {
  '*': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess'],

  // By passing policies for database adapter query:
  'database/adapter': true,
  'metrics/metrics': true,

  // Bypass the `is-logged-in` policy for:
  'auth/login': true, // allow auth/* route to be accessed without isAuthorized
  'init/ping': true,

  // By passing the sync controller requests
  'sync/mode-change': true,
  'sync/sync-recipe': true,

  'notification/create-alert': true,

  /**User service*/
  'user/reset-password': true,

  // Event service
  'event/mqtt-event': true,
  'configurator/delete-system-category': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess', 'isSuperAdmin'],
  'configurator/delete-configurator-system': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess', 'canDeleteConfigurator'],
  'configurator/save-system-data-tagging': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess', 'isConfiguratorSystemEditable'],
  'controlsRelationshipConfig/fetch-control-relationship-driver': [
    'hasValidToken',
    'canReadConfig',
  ],
  'controlsRelationshipConfig/sync-component-control-relationship': [
    'isAuthorized', 'hasSiteAccess', 'hasResourceAccess',
    'canCreateConfig',
  ],
  'controlsRelationshipConfig/sync-control-relationship': ['hasValidToken', 'canCreateConfig'],
  'controlsRelationshipConfig/store-command-relationship-by-driver': [
    'hasValidToken',
    'canCreateConfig',
  ],
  'controlsRelationshipConfig/download-driver-controls-relationship': [
    'hasValidToken',
    'canReadConfig',
  ],
  'controlsRelationshipConfig/crud-control-relationship-by-driver': [
    'hasValidToken',
    'canCreateConfig',
  ],
  'controlsRelationshipConfig/sync-control-relationship-by-site': [
    'isAuthorized',
    'canCreateConfig',
  ],
  'controls/send-command-to-control-asset': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess', 'canSendCommand'],
  'component/change-mode-of-asset-control': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess', 'canChangeMode'],
  'controls/jouletrack-command-feedback-handler-webhook': true,
  'datadevice/recent-data-over-mqtt': true,
  'superRecipe/handle-recipe-feedback': true,
  'bmsx/discovery-result': true,
  'component/fetch-component-mode-details-from-jouletrack-api': ['canCallableFromJouleTrackApi'],

  'configuratorPage/save-svg-page-data-tagging': [
    'isAuthorized', 'hasSiteAccess', 'hasResourceAccess',
    'isConfiguratorPageInEditableMode',
  ],

  'configuratorPage/delete-configurator-page': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess', 'canDeleteConfigurator'],
  'configuratorTable/delete-table': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess', 'isConfiguratorTableEditable'],
  'configuratorPage/publish-configurator-page': ['isAuthorized', 'hasSiteAccess', 'hasResourceAccess', 'canChangeConfiguratorPageState'],
  'configuratorPage/unpublish-configurator-page': [
    'isAuthorized', 'hasSiteAccess', 'hasResourceAccess',
    'canChangeConfiguratorPageState',
  ],
  'component/get-control-relationship-by-component': ['IotAccessControl'],
  'externalDataExchange/*': ['externalDataExchangePolicy'],
  'component/user-config-data-param': true
};
