module.exports = {

  primaryKey: 'siteId',

  'attributes': {
    'siteId': {
      'type': 'string',
      'description': 'hash',
      required: true,
      example: 'ssh'
    },
    'deviceId_abbr': {
      'type': 'string',
      'description': 'range'
    },
    'abbr': {
      'type': 'string'
    },
    'deviceId': {
      'type': 'string'
    },
    'index': 'string',
    'driver': 'string',
    'mulFactor': 'string',
    'offset': 'number',
    'min': 'string',
    'max': 'string',
    'displayName': 'string',
    'unit': 'string',
    'address': 'string',
    'inheritedFrom': 'string',
    'utilityType': 'string',
    'mode': 'string',
    'errOffset': 'string',
    'paramGroup': 'string',
    'filter_existence': 'string',
    'filter_oldVal': 'string',
    'filter_variance': 'string',
    'statePreference': 'string',
    'functionCode': 'string',
    'lcdLabel': 'string',
    'rawUnit': 'string',
    'dau': 'string',
    'operation': 'string',
    'regType': 'number',
    'secDriver': 'string',
    'assetId': 'string',
    'paramType': 'string',
    'isBatch': 'string',
    'batch': 'string',
    'bitIndex': 'string',
    'batchDelayInMilliSeconds': 'string',
    "tolerance": "string",
    'componentId': 'string', // Used for storing componentIds generated via bulk component configuration for MBIP
    'decimalPrecision': {
      'type': 'number',
      required: false,
      allowNull: true, // If we remove allowNull to true, then it will set default value as 0
    },
    'objectRefId': "number",
    'matchParam': "string",
  },
  beforeCreate: function (values, next) {
    if (values.hasOwnProperty('decimalPrecision') && (values['decimalPrecision'] === undefined || values['decimalPrecision'] === null)) {
      delete values['decimalPrecision'];
    }
    return next();
  },
  beforeUpdate: function (values, next) {
    if (values.hasOwnProperty('decimalPrecision') && (values['decimalPrecision'] === undefined || values['decimalPrecision'] === null)) {
      delete values['decimalPrecision'];
    }
    return next();
  },
};
