const siteService = require("./site.private");
const utils = require("../../utils/site/utils");
const globalHelpers = require("../../utils/globalhelper");
const selfutils = require("../../utils/site/add-new-site.util");
const sitePrivateService = require("./site.private");
const { getDeviceCatBySiteId } = require("../device/device.public");
const { getComponentCatBySiteId } = require("../component/component.service");
const { getAllDeviceTypes } = require("../devicetype/devicetype.public");
const cacheService = require("../cache/cache.service");
const userSiteMapPublic = require("../userSiteMap/userSiteMap.public");
const cacheTTL = 7 * 60;
const currentSiteCacheTTL = 7 * 60;
const isValidSite = async (siteId) => {
  const site = await sitePrivateService.findOne({
    siteId: siteId,
  });
  return site ? true : false;
};

module.exports = {
  create: siteService.create,
  async find(searchParams) {
    try {
      const sites = await siteService.find(searchParams);
      if (!sites || sites.length === 0) {
        return [];
      }
      return sites.map(utils.convertUnparsedDynamoSiteToJSON);
    } catch (e) {
      throw new Error(e);
    }
  },
  async findOne(searchParams) {
    try {
      const site = await siteService.findOne(searchParams);
      if (!site) return;
      return utils.convertUnparsedDynamoSiteToJSON(site);
    } catch (e) {
      throw new Error(e);
    }
  },
  update: async function (param,updateValue) {
    await siteService.update(param,updateValue);
    const { siteId, siteName = "" } = param;
    const currentSiteCacheKey = `siteId:${siteId}:currentSiteDetail`;
    const cacheKey = `siteId:${siteId}:name`;
    await cacheService.set(cacheKey, siteName);
    await cacheService.expire(cacheKey, cacheTTL);
    await cacheService.delete(currentSiteCacheKey);
  },
  delete: siteService.delete,

  /**
   * @description Returns a promise which resolves to true if the passed siteId exists.
   * @param {string} siteId
   */
  async siteExists(siteId) {
    try {
      const site = await this.findOne(siteId);
      if (site) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  },

  /**
   * @description This function genearte siteId for new site added
   * @param {Array} possibleSiteIds Array of possible siteIds generated
   */
  async findAvailableSiteId(siteName, location) {
    let site;
    let siteId;
    const possibleSiteIds = selfutils.generatePossibleSiteIds(siteName, location);

    for (let i = 0; i < possibleSiteIds.length; i++) {
      site = await siteService.findOne({ siteId: possibleSiteIds[i] });
      if (site === undefined) {
        siteId = possibleSiteIds[i];
        break;
      }
    }
    if (siteId === undefined) {
      throw new Error("Error! [site > addNewSite] Could not generate SiteID");
    } else {
      return siteId;
    }
  },

  // remove a controller from old region and add it to new region
  async updateControllersRegion(siteId, controllerId, oldRegion, newRegion) {
    try {
      const site = await this.findOne({ siteId });
      if (oldRegion === newRegion) return true; // no need to change
      const oldRegionObj = site.regions[oldRegion];
      const newRegionObj = site.regions[newRegion];
      if (globalHelpers.isNullishArray([oldRegionObj, newRegionObj])) {
        return false;
      }

      // remove a the value 'controllerId' from oldregion controllers array.
      oldRegionObj.controller = Array.isArray(oldRegionObj.controller)
        ? oldRegionObj.controller.filter((_controllerId) => _controllerId !== controllerId)
        : [];

      // Add new controllerId to newRegion controllers Array
      newRegionObj.controller = Array.isArray(newRegionObj.controller)
        ? newRegionObj.controller
        : [];
      newRegionObj.controller.push(controllerId);
      newRegionObj.controller = newRegionObj.controller.filter(globalHelpers.unique); // remove duplicate values

      site.regions[oldRegion] = oldRegionObj;
      site.regions[newRegion] = newRegionObj;
      await siteService.update({ siteId }, { regions: site.regions });
      return true;
    } catch (e) {
      sails.log.error(e);
      return false;
    }
  },

  async getSitesConsumptionUnit(siteId) {
    try {
      const site = await siteService.findOne({ siteId });
      if (site === undefined) {
        throw new Error("Not a valid site id");
      }

      return site.consumptionUnit || "kvah";
    } catch (e) {
      sails.log.erroe("siteservice::getSitesConsumptionUnit ", e);
      throw e;
    }
  },

  isValidSite: isValidSite,
  /**
   * @description fetchAllDeviceComponentsCategories
   * @param {String} siteId
   * @returns
   */
  async getAllDeviceComponentCategories(siteId) {
    const cacheKey = `siteId:${siteId}:available_device_component_categories`;
    const deviceComponentCategories = await getDeviceComponentCategoriesCache(cacheKey);
    if (!_.isEmpty(deviceComponentCategories)) return deviceComponentCategories;

    if (!(await isValidSite(siteId))) throwExcexptionInvalidSite(siteId);

    const [devicesCatList, componentCatList, driverCatMap] = await Promise.all([
      getDeviceCatBySiteId(siteId),
      getComponentCatBySiteId(siteId),
      getAllDeviceTypes(),
    ]);

    const response = [
      ..._buildCategories(devicesCatList, driverCatMap.devices, "device"),
      ..._buildCategories(componentCatList, driverCatMap.components, "component"),
    ];
    await setDeviceComponentCategoriesCache(cacheKey, response);
    return response;

    function _buildCategories(deviceComponentList, deviceTypeCatMap, className) {
      const categories = [];
      deviceComponentList.forEach((item) => {
        const categoryInfo = {};
        const { driverType, deviceType } = item;
        categoryInfo.class = className;
        let deviceTypeCatName;
        if (
          deviceTypeCatMap.hasOwnProperty(deviceType) &&
          deviceTypeCatMap[deviceType].hasOwnProperty(driverType) &&
          deviceTypeCatMap[deviceType][driverType].hasOwnProperty("name")
        ) {
          deviceTypeCatName = deviceTypeCatMap[deviceType][driverType].name;
        }
        if (!deviceTypeCatName) {
          return;
        }
        categoryInfo.name = deviceTypeCatName;
        categoryInfo.deviceType = deviceType;
        categories.push(categoryInfo);
      });
      return categories;
    }

    async function getDeviceComponentCategoriesCache(cacheKey) {
      const deviceTypeOfSite = await cacheService.get(cacheKey);
      if (_.isEmpty(deviceTypeOfSite)) return null;
      return JSON.parse(deviceTypeOfSite);
    }

    async function setDeviceComponentCategoriesCache(cacheKey, response) {
      const minValue = 18 * 3600; // Time in seconds
      const maxValue = 20 * 3600; // Time in seconds

      const cacheTTL = Math.floor(Math.random() * (maxValue - minValue) + minValue);
      await cacheService.set(cacheKey, JSON.stringify(response));
      await cacheService.expire(cacheKey, cacheTTL);
    }
  },
  fetchAllSites: async () => {
    return siteService.find({});
  },
  getCurrentSiteDetail: async function (siteId) {
    const currentSiteCacheKey = `siteId:${siteId}:currentSiteDetail`;
    const currentSiteCache = await cacheService.get(currentSiteCacheKey);
    if (currentSiteCache) return JSON.parse(currentSiteCache);
    const currentSiteDetail = await siteService.findOne({ siteId, status: 1 });
    currentSiteDetail.networks = globalHelpers.toJson(currentSiteDetail.networks);
    currentSiteDetail.areas = globalHelpers.toJson(currentSiteDetail.areas);
    currentSiteDetail.regions = globalHelpers.toJson(currentSiteDetail.regions);
    await cacheService.set(currentSiteCacheKey, JSON.stringify(currentSiteDetail));
    await cacheService.expire(currentSiteCacheKey, currentSiteCacheTTL);
    return currentSiteDetail;
  },
  getSiteName: async function (siteId) {
    const cacheKey = `siteId:${siteId}:name`;
    let siteName = await cacheService.get(cacheKey);
    if (siteName) return { siteId, siteName };
    const siteDetails = await siteService.findOne({ siteId });
    siteName = siteDetails?.siteName;
    if (!siteName) {
      return null;
    }
    await cacheService.set(cacheKey, siteName);
    await cacheService.expire(cacheKey, cacheTTL);
    return { siteId, siteName };
  },
  deleteSite: async function (siteId) {
    const isSiteExist = await siteService.findOne({ siteId });
    if (_.isEmpty(isSiteExist)) {
      return true;
    }

    await siteService.update(
      {
        siteId,
      },
      {
        status: 0,
      }
    );

    const cacheKey = `siteId:${siteId}:name`;
    const currentSiteCacheKey = `siteId:${siteId}:currentSiteDetail`;
    await cacheService.delete(cacheKey);
    await cacheService.delete(currentSiteCacheKey);
    const userSites = (
      await userSiteMapPublic.find({
        siteId,
      })
    ).filter((user) => user.siteId === siteId);

    if (_.isEmpty(userSites)) return true;

    const $deleteUserSiteMap = userSites.map((user) =>
      userSiteMapPublic.update(
        {
          siteId,
          userId: user.userId,
        },
        { status: 0 }
      )
    );

    await Promise.all($deleteUserSiteMap);
    return true;
  },
};
