const flaverr = require("flaverr");
const siteService = require("../site/site.service");
const dynamoKeystoreService = require("../dynamokeystore/dynamokeystore.service");
const deviceService = require("../device/device.service");
const iotCoreService = require("../iotCore/iotCore.service");
const s3Service = require("../s3/s3.service");
const { v4: uuidv4 } = require("uuid");
const moment = require("moment-timezone");
const BACnetDiscoveredDeviceManager = require("../../services/bmsx/BACnetDiscoveredDeviceManager");
const discoveredData = require("../../../list_configured_tags.json");

module.exports = {

  /**
   * Fetch discovered devices for a given slave controller
   * @param {string} siteId - Site ID
   * @param {string} slaveControllerId - Slave controller ID (deviceId in DynamoDB)
   * @returns {Object} Object containing discovered devices array and discovery status information
   */
  fetchDiscoveredDevices: async function (siteId, slaveControllerId) {
    try {
      // Step 1: Check if site is IBMS to determine location structure
      const siteInfo = await siteService.findOne({ siteId, status: 1 });
      const isIBMS = siteInfo && siteInfo.industryType && siteInfo.industryType.includes("ibms");

      // Step 2: Get BMS connector
      const connectors = await BMSConnector.find({
        slaveControllerId: parseInt(slaveControllerId),
        siteId: siteId,
        status: 1,
      });

      if (!connectors || connectors.length === 0) {
        sails.log.warn(
          `No BMS connector found for slave controller ${slaveControllerId} in site ${siteId}`
        );
        return {
          devices: [],
          discoveryStatus: {
            lastSyncTs: null,
            isDiscovering: false
          }
        };
      }

      const connector = connectors[0];
      const connectorId = connector.id;

      // Check for timed out discovery requests and handle them
      if (connector.discoveryStatus === 1) {
        await this.checkAndHandleDiscoveryTimeout(connector);
        // Refresh connector data after potential timeout handling
        const refreshedConnector = await BMSConnector.findOne({
          id: connectorId
        });
        if (refreshedConnector) {
          Object.assign(connector, refreshedConnector);
        }
      }

      // Step 3: Get discovered devices - show both:
      // 1. Tagged devices (have refDeviceId) regardless of status (includes deleted but previously tagged)
      // 2. Untagged active devices (no refDeviceId but status = 1)
      const discoveredDevices = await BMSDevice.find({
        bmsConnectorRefId: connectorId,
        or: [
          { refDeviceId: { '>': 0 } }, // Tagged devices (any status)
          { refDeviceId: 0, status: 1 } // Untagged but active devices
        ]
      }).sort("id ASC");

      if (!discoveredDevices || discoveredDevices.length === 0) {
        sails.log.info(`No discovered devices found for connector ${connectorId}`);
        return {
          devices: [],
          discoveryStatus: {
            lastSyncTs: connector.lastSyncTs,
            isDiscovering: connector.discoveryStatus === 1
          }
        };
      }

      // Step 4: Get configured devices using ref_device_id from discovered devices
      const configuredDevices = await this.getConfiguredDiscoveredDevices(
        siteId,
        discoveredDevices,
        isIBMS
      );

      // Step 5: Build the response format
      const devices = this.buildDiscoveredDeviceResponse(
        discoveredDevices,
        configuredDevices,
        isIBMS
      );

      // Step 6: Return devices with discovery status information
      return {
        devices: devices,
        discoveryStatus: {
          lastSyncTs: connector.lastSyncTs,
          isDiscovering: connector.discoveryStatus === 1
        }
      };
    } catch (error) {
      sails.log.error("[BMS Service] Error fetching discovered devices:", error);
      throw error;
    }
  },

  /**
   * Get configured discovered devices using ref_device_id from BMSDevice table
   * @param {string} siteId - Site ID
   * @param {Array} discoveredDevices - Discovered devices from PostgreSQL
   * @param {boolean} isIBMS - Whether the site is IBMS
   * @returns {Object} Map of discovered device ID to DynamoDB device info
   */
  getConfiguredDiscoveredDevices: async function (siteId, discoveredDevices, isIBMS) {
    try {
      const configuredDevicesMap = {};

      for (const discoveredDevice of discoveredDevices) {
        if (discoveredDevice.refDeviceId) {
          try {
            // Get the DeJoule device (third-party master controller) from DynamoDB
            const thirdPartyMasterController = await deviceService.findOne({
              deviceId: discoveredDevice.refDeviceId.toString(),
              siteId: siteId,
            });

            if (thirdPartyMasterController) {
              let locationInfo = {
                deviceId: thirdPartyMasterController.deviceId,
              };

              if (isIBMS) {
                // For IBMS sites, get leafNodeId from the hierarchy
                locationInfo.leafNodeId = await this.getLeafNodeIdForDevice(
                  thirdPartyMasterController.deviceId,
                  siteId
                );
              } else {
                // For non-IBMS sites, use areaId and regionId
                locationInfo.areaId = thirdPartyMasterController.areaId;
                locationInfo.regionId = thirdPartyMasterController.regionId;
              }

              configuredDevicesMap[discoveredDevice.id.toString()] = locationInfo;
            }
          } catch (deviceError) {
            sails.log.warn(
              `[BMS Service] Could not find third-party master controller ${discoveredDevice.refDeviceId} for discovered device ${discoveredDevice.id}:`,
              deviceError
            );
            // Continue processing other devices
          }
        }
      }

      return configuredDevicesMap;
    } catch (error) {
      sails.log.error("[BMS Service] Error getting configured devices via ref_device_id:", error);
      throw error;
    }
  },

  /**
   * Get leaf node ID for IBMS device from hierarchy
   * @param {string} deviceId - Device ID
   * @param {string} siteId - Site ID
   * @returns {string|null} Leaf node ID or null
   */
  getLeafNodeIdForDevice: async function (deviceId, siteId) {
    try {
      // Query the nodes table to find the parent leaf node that contains this controller
      const query = `
        SELECT parent_id as leaf_node_id
        FROM nodes
        WHERE device_id = $1 AND site_id = $2 AND level_type = 'controller' AND is_deleted = false
        LIMIT 1
      `;

      const result = await sails
        .getDatastore("postgres")
        .sendNativeQuery(query, [deviceId, siteId]);

      return result.rows && result.rows.length > 0 ? result.rows[0].leaf_node_id.toString() : null;
    } catch (error) {
      sails.log.error("[BMS Service] Error getting leaf node ID:", error);
      return null;
    }
  },

  /**
   * Build the response format for discovered devices
   * @param {Array} discoveredDevices - Discovered devices from PostgreSQL
   * @param {Object} configuredDevices - Configured devices from DynamoDB
   * @param {boolean} isIBMS - Whether the site is IBMS
   * @returns {Array} Formatted response array
   */
  buildDiscoveredDeviceResponse: function (discoveredDevices, configuredDevices, isIBMS) {
    try {
      // Build response
      const response = discoveredDevices.map((device) => {
        const discoveredDeviceId = device.id.toString();
        const configuredDevice = configuredDevices[discoveredDeviceId];

        // Build device properties object from BMSDevice columns
        const deviceProperties = {
          name: device.name,
          address: device.address,
          vendorName: device.vendorName,
          modelName: device.modelName,
          systemStatus: device.systemStatus,
          description: device.description,
          location: device.location,
        };

        // Build location object based on site type
        let location = null;
        if (configuredDevice) {
          if (isIBMS) {
            location = {
              areaId: null,
              regionId: null,
              leafNodeId: configuredDevice.leafNodeId,
            };
          } else {
            location = {
              areaId: configuredDevice.areaId,
              regionId: configuredDevice.regionId,
              leafNodeId: null,
            };
          }
        }

        return {
          discoveredDeviceId: discoveredDeviceId,
          name: device.name,
          controllerId: configuredDevice ? configuredDevice.deviceId : null,
          location: location,
          deviceProperties: deviceProperties,
          controllerBMSXConnectivityStatus: device.status,
        };
      });

      return response;
    } catch (error) {
      sails.log.error("[BMS Service] Error building response:", error);
      throw error;
    }
  },

  /**
   * Configure discovered devices - handles both newly discovered devices and existing third-party master controller location updates
   * @param {string} siteId - Site ID
   * @param {string} slaveControllerId - Slave controller ID
   * @param {string} deviceType - Device type for all devices
   * @param {string} vendorId - Vendor ID for all devices
   * @param {Array} devices - Array of devices to configure
   * @returns {Object} Configuration results
   */
  configureDiscoveredDevices: async function (
    siteId,
    slaveControllerId,
    deviceType,
    vendorId,
    devices
  ) {
    // Validate site exists
    const site = await siteService.findOne({ siteId, status: 1 });
    if (!site) {
      throw flaverr("E_NOT_FOUND", new Error("Site not found"));
    }

    // Check if site is IBMS to determine location structure
    const isIBMS = site.industryType && site.industryType.includes("ibms");

    this.validateDeviceInputs(devices, isIBMS);

    // Validate slave controller exists
    const slaveController = await deviceService.findOne({
      deviceId: slaveControllerId,
      siteId: siteId,
    });

    if (!slaveController || !slaveController.isSlaveController) {
      throw flaverr("E_NOT_FOUND", new Error("Slave controller not found"));
    }

    // Separate new discovered devices from existing third-party master controllers
    const newDiscoveredDevices = devices.filter((device) => !device.controllerId);
    const existingControllers = devices.filter((device) => device.controllerId);

    const results = {
      masterControllersCreated: [],
      masterControllersUpdated: [],
      errors: [],
    };

    // Process new discovered devices (create as third-party master controllers)
    let newDeviceResults = { created: [] };
    if (newDiscoveredDevices.length > 0) {
      newDeviceResults = await this.processNewDiscoveredDevices(
        newDiscoveredDevices,
        site,
        slaveController,
        isIBMS,
        deviceType,
        vendorId
      );
      results.masterControllersCreated = newDeviceResults.created.map(({ deviceConfig, ...rest }) => rest);
      results.errors.push(...newDeviceResults.errors);
    }

    // Process existing third-party master controllers (location updates)
    let devicesWithLocationChanges = [];
    if (existingControllers.length > 0) {
      const existingDeviceResults = await this.processExistingDiscoveredDevices(
        existingControllers,
        site,
        isIBMS
      );
      results.masterControllersUpdated = existingDeviceResults.updated;
      results.errors.push(...existingDeviceResults.errors);
      devicesWithLocationChanges = existingDeviceResults.devicesWithLocationChanges || [];
    }

    // Combined batch site update for both new devices and existing device location changes (for non-IBMS sites)
    if (!isIBMS && (newDeviceResults.created.length > 0 || devicesWithLocationChanges.length > 0)) {
      try {
        await this.batchUpdateSiteReferencesForAllDevices(
          site,
          newDeviceResults.created,
          devicesWithLocationChanges
        );
      } catch (siteUpdateError) {
        sails.log.error(
          "Error updating site references for batch of all controllers:",
          siteUpdateError
        );
        // Don't fail the entire operation, but log the error
      }
    }

    return results;
  },

  /**
   * Validate device inputs based on site type (IBMS vs non-IBMS)
   * @param {Array} devices - Array of devices to validate
   * @param {boolean} isIBMS - Whether site is IBMS
   */
  validateDeviceInputs: function (devices, isIBMS) {
    for (const device of devices) {
      const { location } = device;

      if (!location) {
        throw flaverr("E_BAD_REQUEST", new Error("Location is required for device configuration"));
      }

      if (isIBMS) {
        if (!location.leafNodeId) {
          throw flaverr("E_BAD_REQUEST", new Error("For IBMS sites, leafNodeId is required"));
        }
      } else {
        if (!location.areaId || !location.regionId) {
          throw flaverr(
            "E_BAD_REQUEST",
            new Error("For non-IBMS sites, areaId and regionId are required")
          );
        }
      }
    }
  },

  /**
   * Process new discovered devices - create them as third-party master controllers
   * @param {Array} newDiscoveredDevices - Array of new discovered devices to create as third-party master controllers
   * @param {Object} site - Site object
   * @param {Object} slaveController - Slave controller
   * @param {boolean} isIBMS - Whether site is IBMS
   * @param {string} deviceType - Device type for all devices
   * @param {string} vendorId - Vendor ID for all devices
   * @returns {Object} Results with created devices and errors
   */
  processNewDiscoveredDevices: async function (
    newDiscoveredDevices,
    site,
    slaveController,
    isIBMS,
    deviceType,
    vendorId
  ) {
    const created = [];
    const errors = [];

    // Validate BMS connector exists for this site and slave controller upfront
    const bmsConnector = await BMSConnector.findOne({
      siteId: site.siteId,
      slaveControllerId: parseInt(slaveController.deviceId),
      status: 1,
    });

    if (!bmsConnector) {
      // If no valid connector, all devices will fail - return early
      const error = `No active BMS connector found for site ${site.siteId} and slave controller ${slaveController.deviceId}`;
      newDiscoveredDevices.forEach((deviceData) => {
        errors.push({
          device: deviceData,
          error: error,
        });
      });
      return { created, errors };
    }

    // Get device type count mapping for generic naming (like legacy addController)
    const deviceTypeCountMap = await this.getDeviceTypeCountMap(site.siteId);

    for (const deviceData of newDiscoveredDevices) {
      try {
        const { discoveredDeviceId, location } = deviceData;

        if (!discoveredDeviceId) {
          throw flaverr(
            "E_BAD_REQUEST",
            new Error(`Device missing required field: discoveredDeviceId is required`)
          );
        }

        // Validate that the discoveredDeviceId exists in BMSDevice table, belongs to correct connector
        const existingBMSDevice = await BMSDevice.findOne({
          id: parseInt(discoveredDeviceId),
          bmsConnectorRefId: bmsConnector.id,
          status: 1,
        });

        if (!existingBMSDevice) {
          throw flaverr(
            "E_BAD_REQUEST",
            new Error(
              `Discovered device with ID ${discoveredDeviceId} does not exist, does not belong to the correct BMS connector, or is inactive`
            )
          );
        }

        // Check if this discovered device is already configured (has refDeviceId)
        if (existingBMSDevice.refDeviceId && existingBMSDevice.refDeviceId != '0') {
          throw flaverr(
            "E_BAD_REQUEST",
            new Error(
              `Discovered device with ID ${discoveredDeviceId} is already configured with refDeviceId ${existingBMSDevice.refDeviceId}`
            )
          );
        }

        const controllerName = await this.generateControllerName(deviceType, deviceTypeCountMap);

        // Determine location based on site type and provided data
        let locationConfig = {};
        if (isIBMS) {
          locationConfig.regionId = "ibms";
          locationConfig.areaId = "ibms";
          locationConfig.leafNodeId = location.leafNodeId;
        } else {
          locationConfig.areaId = location.areaId;
          locationConfig.regionId = location.regionId;

          // Validate location exists in site
          if (!site.regions || !site.regions[locationConfig.regionId]) {
            throw flaverr(
              "E_BAD_REQUEST",
              new Error(`Region '${locationConfig.regionId}' does not exist in site`)
            );
          }
          if (!site.areas || !site.areas[locationConfig.areaId]) {
            throw flaverr(
              "E_BAD_REQUEST",
              new Error(`Area '${locationConfig.areaId}' does not exist in site`)
            );
          }
        }

        const nextDeviceId = await sails.helpers.generateNextDeviceId();

        // Create device configuration for discovered third-party master controller
        const deviceConfig = {
          deviceId: String(nextDeviceId),
          siteId: site.siteId,
          name: controllerName,
          deviceType: deviceType,
          vendorId: vendorId,
          networkId: slaveController.networkId,
          ...locationConfig,
          secondaryControllerId: parseInt(slaveController.deviceId),
          isSlaveController: 0,
          // Hardcoded values for backward compatibility
          baudRate: "9600",
          hardwareVer: "v1",
          stopbit: "1",
          softwareVer: "latest",
          modbusFromUSB: false,
          operationMode: "standalone",
          parity: "N",
        };

        // Create the new third‑party master controller
        await deviceService.create(deviceConfig);

        // For IBMS sites, also create entry in nodes table for hierarchy
        if (isIBMS) {
          await this.createNodeForIBMSController(
            String(nextDeviceId),
            controllerName,
            location.leafNodeId,
            site.siteId
          );
        }

        // Update BMSDevice.refDeviceId to link to the new controller
        await BMSDevice.updateOne({
          id: parseInt(discoveredDeviceId),
        }).set({
          refDeviceId: parseInt(nextDeviceId),
        });

        created.push({
          deviceId: String(nextDeviceId),
          name: controllerName,
          discoveredDeviceId: discoveredDeviceId,
          status: "created",
          deviceConfig: deviceConfig, // Store config for batch site update
        });
      } catch (deviceError) {
        errors.push({
          device: deviceData,
          error: deviceError.message,
        });
      }
    }

    return { created, errors };
  },

  /**
   * Process existing third-party master controllers - update their locations
   * @param {Array} existingDevices - Array of existing third-party master controllers to update
   * @param {Object} site - Site object
   * @param {boolean} isIBMS - Whether site is IBMS
   * @returns {Object} Results with updated devices and errors
   */
  processExistingDiscoveredDevices: async function (existingDevices, site, isIBMS) {
    const updated = [];
    const errors = [];
    const devicesWithLocationChanges = []; // Collect devices with location changes for batch site update

    for (const deviceData of existingDevices) {
      try {
        const { controllerId, location } = deviceData;

        if (!controllerId) {
          throw flaverr(
            "E_BAD_REQUEST",
            new Error("Controller ID is required for existing devices")
          );
        }

        // Find the existing third-party master controller
        const existingDevice = await deviceService.findOne({
          deviceId: controllerId,
          siteId: site.siteId,
        });

        if (!existingDevice) {
          errors.push({
            device: deviceData,
            error: `Device with ID '${controllerId}' not found`,
          });
          continue;
        }

        const correspondingBMSDevice = await BMSDevice.findOne({
          refDeviceId: parseInt(controllerId),
          status: 1,
        });

        if (!correspondingBMSDevice) {
          errors.push({
            device: deviceData,
            error: `No active BMS device found linked to controller ${controllerId}`,
          });
          continue;
        }

        // Prepare update data based on site type
        let updateData = {};
        let locationChanged = false;

        if (isIBMS) {
          if (location.leafNodeId !== existingDevice.leafNodeId) {
            updateData.leafNodeId = location.leafNodeId;
            locationChanged = true;
          }
        } else {
          if (location.areaId !== existingDevice.areaId) {
            if (!site.areas || !site.areas[location.areaId]) {
              throw flaverr(
                "E_BAD_REQUEST",
                new Error(`Area '${location.areaId}' does not exist in site`)
              );
            }
            updateData.areaId = location.areaId;
            locationChanged = true;
          }

          if (location.regionId !== existingDevice.regionId) {
            if (!site.regions || !site.regions[location.regionId]) {
              throw flaverr(
                "E_BAD_REQUEST",
                new Error(`Region '${location.regionId}' does not exist in site`)
              );
            }
            updateData.regionId = location.regionId;
            locationChanged = true;
          }
        }

        // Only update if there are changes
        if (Object.keys(updateData).length === 0) {
          continue;
        }

        // Update the device using service layer (DynamoDB adapter doesn't support direct model updates)
        await deviceService.update(
          {
            deviceId: controllerId,
            siteId: site.siteId,
          },
          updateData
        );

        // Get the updated device manually
        const updatedDevice = await deviceService.findOne({
          deviceId: controllerId,
          siteId: site.siteId,
        });

        // Collect devices with location changes for batch site update (for non-IBMS sites)
        if (locationChanged && !isIBMS) {
          devicesWithLocationChanges.push({
            oldDevice: existingDevice,
            newDevice: updatedDevice,
          });
        }

        // For IBMS sites, also update the nodes table if leafNodeId changed
        if (isIBMS && locationChanged && location.leafNodeId) {
          await this.updateNodeForIBMSController(
            existingDevice.deviceId,
            location.leafNodeId,
            site.siteId
          );
        }

        // Update DeJoule end devices if location changed
        let dejouleDevicesUpdated = 0;
        if (locationChanged) {
          dejouleDevicesUpdated = await this.updateDeJouleDevicesLocation(updatedDevice, isIBMS);
        }

        updated.push({
          deviceId: updatedDevice.deviceId,
          name: updatedDevice.name,
          changes: updateData,
          dejouleDevicesUpdated: dejouleDevicesUpdated,
          status: "updated",
        });
      } catch (deviceError) {
        errors.push({
          device: deviceData,
          error: deviceError.message,
        });
      }
    }

    return { updated, errors, devicesWithLocationChanges };
  },





  /**
   * Combined batch update site references for both new devices and existing devices with location changes
   * @param {Object} site - Site object (already converted from JSON strings to objects)
   * @param {Array} newDevices - Array of newly created devices with their configs
   * @param {Array} devicesWithLocationChanges - Array of devices with location changes: {oldDevice, newDevice} objects
   */
  batchUpdateSiteReferencesForAllDevices: async function (site, newDevices, devicesWithLocationChanges) {
    try {
      // Create deep copies to avoid mutating the original site object
      const updatedNetworks = JSON.parse(JSON.stringify(site.networks || {}));
      const updatedRegions = JSON.parse(JSON.stringify(site.regions || {}));

      // Process new devices (add to regions)
      for (const newDevice of newDevices) {
        const { deviceId, deviceConfig } = newDevice;

        // Add device to network (for consistency with SJPL controllers)
        if (deviceConfig.networkId && updatedNetworks[deviceConfig.networkId]) {
          if (!updatedNetworks[deviceConfig.networkId].includes(String(deviceId))) {
            updatedNetworks[deviceConfig.networkId].push(String(deviceId));
          }
        }

        // Add device to region controllers (regions have controller arrays, areas do not)
        if (
          updatedRegions[deviceConfig.regionId] &&
          updatedRegions[deviceConfig.regionId].controller
        ) {
          if (!updatedRegions[deviceConfig.regionId].controller.includes(String(deviceId))) {
            updatedRegions[deviceConfig.regionId].controller.push(String(deviceId));
          }
        }
      }
      for (const change of devicesWithLocationChanges) {
        const { oldDevice, newDevice } = change;
        const deviceId = newDevice.deviceId;

        // Remove device from old region if region changed
        if (oldDevice.regionId !== newDevice.regionId) {
          if (
            oldDevice.regionId &&
            updatedRegions[oldDevice.regionId] &&
            updatedRegions[oldDevice.regionId].controller
          ) {
            updatedRegions[oldDevice.regionId].controller = updatedRegions[
              oldDevice.regionId
            ].controller.filter((id) => id !== deviceId);
          }

          // Add device to new region
          if (
            newDevice.regionId &&
            updatedRegions[newDevice.regionId] &&
            updatedRegions[newDevice.regionId].controller
          ) {
            if (!updatedRegions[newDevice.regionId].controller.includes(deviceId)) {
              updatedRegions[newDevice.regionId].controller.push(deviceId);
            }
          }
        }
      }

      // Single site update for all devices
      await siteService.update(
        { siteId: site.siteId },
        {
          networks: JSON.stringify(updatedNetworks),
          regions: JSON.stringify(updatedRegions),
        }
      );

      sails.log.info(
        `Combined batch updated site references for ${newDevices.length} new devices and ${devicesWithLocationChanges.length} existing devices on site ${site.siteId}`
      );
    } catch (error) {
      sails.log.error(
        `Unable to batch update site references for all devices on site ${site.siteId}:`,
        error
      );
      throw error;
    }
  },

  /**
   * Update DeJoule end devices location when their parent third-party master controller location changes
   * @param {Object} updatedMasterController - Updated third-party master controller
   * @param {boolean} isIBMS - Whether site is IBMS
   * @returns {number} Number of DeJoule devices updated
   */
  updateDeJouleDevicesLocation: async function (updatedMasterController, isIBMS) {
    try {
      // Find all DeJoule end devices that have this third-party master controller as their controllerId
      const dejouleDevices = await deviceService.find({
        controllerId: updatedMasterController.deviceId,
        siteId: updatedMasterController.siteId,
      });

      if (!dejouleDevices || dejouleDevices.length === 0) {
        sails.log.info(
          `No DeJoule end devices found for third-party master controller ${updatedMasterController.deviceId}`
        );
        return 0;
      }

      sails.log.info(
        `Updating location for ${dejouleDevices.length} DeJoule end devices under master controller ${updatedMasterController.deviceId}`
      );

      const updateOperations = dejouleDevices.map((dejouleDevice) => {
        const updateData = {};

        if (isIBMS) {
          // For IBMS sites, update leafNodeId if it exists
          if (updatedMasterController.leafNodeId) {
            updateData.leafNodeId = updatedMasterController.leafNodeId;
          }
        } else {
          // For non-IBMS sites, update areaId and regionId
          if (updatedMasterController.areaId) {
            updateData.areaId = updatedMasterController.areaId;
          }
          if (updatedMasterController.regionId) {
            updateData.regionId = updatedMasterController.regionId;
          }
        }

        if (Object.keys(updateData).length > 0) {
          return deviceService
            .update(
              {
                deviceId: dejouleDevice.deviceId,
                siteId: dejouleDevice.siteId,
              },
              updateData
            )
            .then(() => {
              sails.log.info(
                `Updated DeJoule device ${dejouleDevice.deviceId} location to match parent controller ${updatedMasterController.deviceId}`
              );
              return { success: true, deviceId: dejouleDevice.deviceId };
            })
            .catch((error) => {
              sails.log.error(`Failed to update DeJoule device ${dejouleDevice.deviceId}:`, error);
              return { success: false, deviceId: dejouleDevice.deviceId, error: error.message };
            });
        }
        return Promise.resolve({
          success: false,
          deviceId: dejouleDevice.deviceId,
          reason: "No changes needed",
        });
      });

      const results = await Promise.allSettled(updateOperations);
      const updatedCount = results.filter(
        (result) => result.status === "fulfilled" && result.value.success
      ).length;

      const failedCount = results.filter(
        (result) =>
          result.status === "rejected" ||
          (result.status === "fulfilled" && !result.value.success && result.value.error)
      ).length;

      if (failedCount > 0) {
        sails.log.warn(
          `${failedCount} DeJoule devices failed to update for master controller ${updatedMasterController.deviceId}`
        );
      }

      return updatedCount;
    } catch (error) {
      sails.log.error(
        `Error updating DeJoule devices location for master controller ${updatedMasterController.deviceId}:`,
        error
      );
      // Don't throw error here to avoid breaking the main update flow
      return 0;
    }
  },

  /**
   * Get device type count mapping for generic naming (like legacy addController)
   * @param {string} siteId - Site ID
   * @returns {Object} Device type count mapping
   */
  getDeviceTypeCountMap: async function (siteId) {
    try {
      const deviceTypeMapping = await this.getDeviceTypeMappingFromKeyStore();

      // Get all existing devices in the site
      const existingDevices = await deviceService.find({ siteId });

      // Initialize count map
      const deviceTypeCountMap = {};

      // Count existing devices by type
      existingDevices.forEach((device) => {
        if (deviceTypeMapping[device.deviceType]) {
          if (!deviceTypeCountMap[device.deviceType]) {
            deviceTypeCountMap[device.deviceType] = 0;
          }

          try {
            // Extract counter from name (e.g., "VirtualN3uronBACnetMQTT-5" -> 5)
            const nameParts = device.name.split("-");
            if (nameParts.length >= 2) {
              const nameId = parseInt(nameParts[nameParts.length - 1]);
              if (!isNaN(nameId)) {
                deviceTypeCountMap[device.deviceType] = Math.max(
                  deviceTypeCountMap[device.deviceType],
                  nameId
                );
              }
            }
          } catch (err) {
            sails.log.error("Error parsing device name for count:", device.name, err);
          }
        }
      });

      return deviceTypeCountMap;
    } catch (error) {
      sails.log.error("Error getting device type count map:", error);
      throw error;
    }
  },

  /**
   * Generate generic controller name using counter (like legacy addController)
   * @param {string} deviceType - Device type
   * @param {Object} deviceTypeCountMap - Device type count mapping
   * @returns {string} Generated controller name
   */
  generateControllerName: async function (deviceType, deviceTypeCountMap) {
    try {
      const deviceTypeMapping = await this.getDeviceTypeMappingFromKeyStore();
      const namePrefix = deviceTypeMapping[deviceType];

      if (!namePrefix) {
        throw flaverr(
          "E_BAD_REQUEST",
          new Error(
            `Unknown device type '${deviceType}'. Please configure mapping in DynamoKeyStore with key 'controllerDeviceTypeMap'`
          )
        );
      }

      // Increment counter for this device type
      if (!deviceTypeCountMap[deviceType]) {
        deviceTypeCountMap[deviceType] = 0;
      }
      deviceTypeCountMap[deviceType] += 1;

      return namePrefix + deviceTypeCountMap[deviceType];
    } catch (error) {
      sails.log.error("Error generating controller name:", error);
      throw error;
    }
  },

  /**
   * Create node entry in PostgreSQL nodes table for IBMS third-party master controller
   * @param {string} deviceId - Device ID of the controller
   * @param {string} controllerName - Name of the controller
   * @param {string} leafNodeId - Parent leaf node ID where this controller should be placed
   * @param {string} siteId - Site ID
   * @returns {Promise<void>}
   */
  createNodeForIBMSController: async function (deviceId, controllerName, leafNodeId, siteId) {
    try {
      // First, get the system_id for this site by finding an existing node
      const systemQuery = `
        SELECT DISTINCT system_id
        FROM nodes
        WHERE site_id = $1 AND is_deleted = false
        LIMIT 1
      `;

      const systemResult = await sails
        .getDatastore("postgres")
        .sendNativeQuery(systemQuery, [siteId]);

      if (!systemResult.rows || systemResult.rows.length === 0) {
        throw new Error(`No system found for site ${siteId}`);
      }

      const systemId = systemResult.rows[0].system_id;

      // Create the node entry for the third-party master controller
      const createNodeQuery = `
        INSERT INTO nodes (system_id, parent_id, level_type, name, device_id, site_id, is_deleted)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `;

      await sails
        .getDatastore("postgres")
        .sendNativeQuery(createNodeQuery, [
          systemId,
          parseInt(leafNodeId),
          'controller',
          controllerName,
          deviceId,
          siteId,
          false
        ]);

      sails.log.info(`[BMS Service] Created node entry for IBMS controller ${deviceId} under parent ${leafNodeId}`);
    } catch (error) {
      sails.log.error(`[BMS Service] Error creating node for IBMS controller ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Update node entry in PostgreSQL nodes table for IBMS third-party master controller
   * @param {string} deviceId - Device ID of the controller
   * @param {string} newLeafNodeId - New parent leaf node ID
   * @param {string} siteId - Site ID
   * @returns {Promise<void>}
   */
  updateNodeForIBMSController: async function (deviceId, newLeafNodeId, siteId) {
    try {
      // Update the parent_id for the controller node
      const updateNodeQuery = `
        UPDATE nodes
        SET parent_id = $1
        WHERE device_id = $2 AND site_id = $3 AND level_type = 'controller' AND is_deleted = false
      `;

      await sails
        .getDatastore("postgres")
        .sendNativeQuery(updateNodeQuery, [parseInt(newLeafNodeId), deviceId, siteId]);

      sails.log.info(`[BMS Service] Updated node parent for IBMS controller ${deviceId} to parent ${newLeafNodeId}`);
    } catch (error) {
      sails.log.error(`[BMS Service] Error updating node for IBMS controller ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Delete node entry in PostgreSQL nodes table for IBMS third-party master controller
   * @param {string} deviceId - Device ID of the controller
   * @param {string} siteId - Site ID
   * @returns {Promise<void>}
   */
  deleteNodeForIBMSController: async function (deviceId, siteId) {
    try {
      // Mark the node as deleted (soft delete)
      const deleteNodeQuery = `
        UPDATE nodes
        SET is_deleted = true
        WHERE device_id = $1 AND site_id = $2 AND level_type = 'controller' AND is_deleted = false
      `;

      await sails
        .getDatastore("postgres")
        .sendNativeQuery(deleteNodeQuery, [deviceId, siteId]);

      sails.log.info(`[BMS Service] Marked node as deleted for IBMS controller ${deviceId}`);
    } catch (error) {
      sails.log.error(`[BMS Service] Error deleting node for IBMS controller ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Check if discovery request has timed out and handle it
   * @param {Object} bmsConnectorDetail - BMS connector detail object
   * @returns {Promise<boolean>} True if timed out and handled, false if still valid
   */
  checkAndHandleDiscoveryTimeout: async function (bmsConnectorDetail) {
    try {
      const DISCOVERY_TIMEOUT_MINUTES = 2;
      const now = moment.tz("UTC");
      const updatedAt = moment.tz(bmsConnectorDetail.updatedAt, "UTC");
      const minutesSinceUpdate = now.diff(updatedAt, 'minutes');

      if (minutesSinceUpdate >= DISCOVERY_TIMEOUT_MINUTES) {
        sails.log.warn(
          `[BMS Service] Discovery request timed out for connector ${bmsConnectorDetail.id}. ` +
          `Started ${minutesSinceUpdate} minutes ago. Marking as failed.`
        );

        // Mark discovery as failed due to timeout
        await BMSConnector.updateOne({
          id: bmsConnectorDetail.id
        }).set({
          discoveryStatus: 3, // failed
          discoveryRequestId: null,
        });

        return true; // Timed out
      }

      return false; // Still valid
    } catch (error) {
      sails.log.error("[BMS Service] Error checking discovery timeout:", error);
      return false; // Assume still valid on error
    }
  },

  /**
   * Get device type mapping from DynamoKeyStore
   * @returns {Object} Device type to name prefix mapping
   */
  getDeviceTypeMappingFromKeyStore: async function () {
    try {
      const mappingData = await dynamoKeystoreService.findOne({ key: "controllerDeviceTypeMap" });

      if (mappingData && mappingData.list) {
        return typeof mappingData.list === "string"
          ? JSON.parse(mappingData.list)
          : mappingData.list;
      }

      // Fallback mapping if not found in DynamoKeyStore
      return {
        "n3uronbacnetmqtt-controller": "VirtualN3uronBACnetMQTT-",
      };
    } catch (error) {
      sails.log.error("Error getting device type mapping from DynamoKeyStore:", error);

      return {
        "n3uronbacnetmqtt-controller": "VirtualN3uronBACnetMQTT-",
      };
    }
  },

  /**
   * Trigger discovery request for BACnet devices via IoT Core
   * @param {string} siteId - Site ID
   * @param {string} slaveControllerId - Slave controller ID (deviceId in DynamoDB)
   * @returns {Object} Result with message and request ID
   */
  triggerDiscoveryRequest: async function (siteId, slaveControllerId) {
    try {
      // Validate slave controller exists
      const slaveController = await deviceService.findOne({
        deviceId: slaveControllerId,
        siteId: siteId,
      });

      if (!slaveController || !slaveController.isSlaveController) {
        throw flaverr("E_NOT_FOUND", new Error("Slave controller not found"));
      }

      // Validate BMS connector exists and get current status
      const bmsConnectorDetail = await BMSConnector.findOne({
        siteId: siteId,
        protocol: slaveController.communicationCategory,
        bmsConnectorClass: slaveController.deviceType,
        slaveControllerId: parseInt(slaveController.deviceId),
        status: 1
      });

      if (!bmsConnectorDetail) {
        throw flaverr("E_NOT_FOUND", new Error("This slave controller is not connected to BMSX database."));
      }

      // Check if discovery is already in progress
      if (bmsConnectorDetail.discoveryStatus === 1) {
        // Check if the current discovery request has timed out (2 minutes)
        const isTimedOut = await this.checkAndHandleDiscoveryTimeout(bmsConnectorDetail);
        if (!isTimedOut) {
          throw flaverr("E_BAD_REQUEST", new Error("Discovery request already in progress. Please wait for completion."));
        }
        // If timed out, the status has been reset to failed, so we can proceed with new request
      }

      const requestId = uuidv4();
      const timestamp = moment.tz("UTC").toISOString();

      // Update discovery status to 'discovering' (1) in BMSConnector
      await BMSConnector.updateOne({
        id: bmsConnectorDetail.id
      }).set({
        discoveryStatus: 1, // discovering
        discoveryRequestId: requestId,
      });

      // Build MQTT topic and payload
      const topic = `${siteId}/request/bmsx/connector-tags`;
      const payload = {
        site_id: siteId,
        protocol: slaveController.communicationCategory,
        slave_controller_id: slaveControllerId,
        connector: slaveController.deviceType,
        operation: "listConnectorTags",
        request_id: requestId,
        ts: timestamp,
      };

      // Publish to IoT Core
      await iotCoreService.publish(topic, payload);

      sails.log.info(`[BMS Service] Discovery request triggered for slave controller ${slaveControllerId}, requestId: ${requestId}`);

      return {
        message: "Discovery request triggered successfully",
        requestId: requestId,
      };
    } catch (error) {
      sails.log.error("[BMS Service] Error triggering discovery request:", error);

      // Clean up on error - reset discovery status
      try {
        const bmsConnectorDetail = await BMSConnector.findOne({
          siteId: siteId,
          slaveControllerId: parseInt(slaveControllerId),
          status: 1
        });

        if (bmsConnectorDetail) {
          await BMSConnector.updateOne({
            id: bmsConnectorDetail.id
          }).set({
            discoveryStatus: 3, // failed
            discoveryRequestId: null,
          });
        }
      } catch (cleanupError) {
        sails.log.error("[BMS Service] Error during cleanup:", cleanupError);
      }

      throw error;
    }
  },

  /**
   * Process discovery result from IoT Core
   * @param {Object} resultData - Discovery result data
   * @returns {Object} Result with message
   */
  processDiscoveryResult: async function (resultData) {
    try {
      const { siteId, slaveControllerId, requestId, status, s3Bucket, s3Object } = resultData;

      // Get BMS connector to validate request
      const bmsConnectorDetail = await BMSConnector.findOne({
        siteId: siteId,
        slaveControllerId: parseInt(slaveControllerId),
        status: 1
      });

      if (!bmsConnectorDetail) {
        throw flaverr("E_NOT_FOUND", new Error("BMS connector not found"));
      }

      // Validate that this request matches the current discovery operation
      if (bmsConnectorDetail.discoveryStatus !== 1 || bmsConnectorDetail.discoveryRequestId !== requestId) {
        throw flaverr("E_BAD_REQUEST", new Error("Invalid or expired discovery request"));
      }

      if (status === 1) {
        // Success - process S3 data
        if (!s3Bucket || !s3Object) {
          throw flaverr("E_BAD_REQUEST", new Error("S3 bucket and object are required for successful discovery"));
        }

        // Process S3 data
        await this.processS3DiscoveryData(siteId, slaveControllerId, s3Bucket, s3Object);

        // Update discovery status to completed (2) with timestamp
        const timestamp = moment.tz("UTC").toDate();
        await BMSConnector.updateOne({
          id: bmsConnectorDetail.id
        }).set({
          discoveryStatus: 2, // completed
          lastSyncTs: timestamp,
          discoveryRequestId: null,
        });

        sails.log.info(`[BMS Service] Discovery completed successfully for slave controller ${slaveControllerId}`);

        return {
          message: "Discovery result processed successfully",
        };
      } else {
        // Failed discovery
        sails.log.error(`[BMS Service] Discovery failed for request ${requestId}`);

        // Update discovery status to failed (3)
        await BMSConnector.updateOne({
          id: bmsConnectorDetail.id
        }).set({
          discoveryStatus: 3, // failed
          discoveryRequestId: null,
        });

        return {
          message: "Discovery request failed",
        };
      }
    } catch (error) {
      sails.log.error("[BMS Service] Error processing discovery result:", error);

      // Clean up on error - set status to failed
      try {
        const bmsConnectorDetail = await BMSConnector.findOne({
          siteId: siteId,
          slaveControllerId: parseInt(slaveControllerId),
          status: 1
        });

        if (bmsConnectorDetail) {
          await BMSConnector.updateOne({
            id: bmsConnectorDetail.id
          }).set({
            discoveryStatus: 3, // failed
            discoveryRequestId: null,
          });
        }
      } catch (cleanupError) {
        sails.log.error("[BMS Service] Error during cleanup:", cleanupError);
      }

      throw error;
    }
  },

  /**
   * Process S3 discovery data by downloading and syncing to PostgreSQL
   * @param {string} siteId - Site ID
   * @param {string} slaveControllerId - Slave controller ID
   * @param {string} s3Bucket - S3 bucket name (bucket_name in MQTT packet)
   * @param {string} s3Object - S3 object key (s3_key in MQTT packet)
   */
  processS3DiscoveryData: async function (siteId, slaveControllerId, s3Bucket, s3Object) {
    try {
      // Input validation
      if (!siteId || !slaveControllerId || !s3Bucket || !s3Object) {
        throw flaverr("E_BAD_REQUEST", new Error("Missing required parameters for S3 data processing"));
      }

      // Download S3 data with timeout and error handling
      let s3Data;
      try {
        s3Data = await s3Service.get(s3Object, s3Bucket);
      } catch (s3Error) {
        throw flaverr("E_BAD_REQUEST", new Error(`Failed to download S3 data: ${s3Error.message}`));
      }

      // Parse JSON data with error handling
      let discoveryData;
      try {
        discoveryData = JSON.parse(s3Data.Body.toString());
      } catch (parseError) {
        throw flaverr("E_BAD_REQUEST", new Error(`Failed to parse S3 JSON data: ${parseError.message}`));
      }

      // Validate discovery data structure
      if (!discoveryData || !discoveryData.devices || !Array.isArray(discoveryData.devices)) {
        throw flaverr("E_BAD_REQUEST", new Error("Invalid discovery data structure - missing or invalid devices array"));
      }

      if (discoveryData.devices.length === 0) {
        sails.log.warn(`[BMS Service] No devices found in discovery data for site ${siteId}`);
        return;
      }

      // Get slave controller details
      const slaveController = await deviceService.findOne({
        deviceId: slaveControllerId,
        siteId: siteId,
      });

      if (!slaveController) {
        throw flaverr("E_NOT_FOUND", new Error(`Slave controller ${slaveControllerId} not found for site ${siteId}`));
      }

      // Get BMS connector
      const bmsConnectorDetail = await BMSConnector.findOne({
        siteId: siteId,
        protocol: slaveController.communicationCategory,
        bmsConnectorClass: slaveController.deviceType,
        slaveControllerId: parseInt(slaveController.deviceId),
        status: 1
      });

      if (!bmsConnectorDetail) {
        throw flaverr("E_NOT_FOUND", new Error(`BMS connector not found for slave controller ${slaveControllerId}`));
      }

      // Create BACnet discovered device manager instance
      const deviceManager = new BACnetDiscoveredDeviceManager({
        siteId: siteId,
        slaveController: slaveController.deviceId,
        discoveredData: discoveryData.devices,
        protocol: slaveController.communicationCategory,
        connectorClass: slaveController.deviceType,
        bmsConnectorRefId: bmsConnectorDetail.id
      });

      // Sync discovery data to PostgreSQL with error handling
      try {
        await deviceManager.syncData();
        sails.log.info(`[BMS Service] Successfully synced ${discoveryData.devices.length} discovered devices for site ${siteId}`);
      } catch (syncError) {
        throw flaverr("E_SERVER_ERROR", new Error(`Failed to sync discovery data to database: ${syncError.message}`));
      }

    } catch (error) {
      sails.log.error(`[BMS Service] Error processing S3 discovery data for site ${siteId}, slave controller ${slaveControllerId}:`, error);
      throw error;
    }
  },

  /**
   * Check if a device is a third-party virtual master controller (BMSX)
   * @param {string} controllerId - Controller ID to check
   * @param {string} siteId - Site ID
   * @returns {Promise<boolean>} True if device is a third-party virtual master controller
   */
  isThirdPartyVirtualMasterController: async function (controllerId, siteId) {
    try {
      if (!controllerId || !siteId) {
        throw flaverr("E_UNPROCESSABLE_ENTITY", new Error("Controller ID and Site ID are required"));
      }

      const controllerDetail = await deviceService.findOne({
        deviceId: controllerId,
        siteId: siteId,
      });

      if (!controllerDetail) {
        throw flaverr("E_UNPROCESSABLE_ENTITY", new Error("Controller not found"));
      }

      const isThirdPartyVirtualMaster =
        controllerDetail.deviceType === "n3uronbacnetmqtt-controller" &&
        controllerDetail.vendorId === "n3uronbacnetmqtt" &&
        !controllerDetail.isSlaveController; // Virtual master controllers are not slave controllers

      return isThirdPartyVirtualMaster;
    } catch (error) {
      sails.log.error("Error checking if device is third-party virtual master controller:", error);
      throw flaverr("E_UNPROCESSABLE_ENTITY", new Error("Error validating controller"));
    }
  },
};
